import { defineComponent } from 'vue';
import Button from '../button';

const Card = defineComponent({
  name: 'QCard',
  props: {
    /**
     * Title
     */
    title: {
      type: String,
      required: true,
    },
  },
  emits: ['click'],
  render() {
    return (
      <div
        onClick={() => {
          this.$emit('click');
        }}
        class="q-card"
      >
        <header>
          <div>{this.title}</div>
        </header>
        <div>{this.$children}</div>
        <div>
          <Button>Extra</Button>
        </div>
      </div>
    );
  },
});

export default Card;
