import { writeFileSync, existsSync, appendFileSync } from "node:fs";
import { resolve, join, dirname, basename } from "node:path";

const COMPONENTS = [
  "affix",
  "alert",
  "anchor",
  "auto-complete",
  "avatar",
  "back-top",
  "badge",
  "breadcrumb",
  "button",
  "calendar",
  "card",
  "carousel",
  "cascader",
  "checkbox",
  "collapse",
  "date-picker",
  "descriptions",
  "divider",
  "drawer",
  "dropdown",
  "empty",
  "form",
  "grid",
  "icon",
  "input",
  "input-number",
  "layout",
  "list",
  "mentions",
  "menu",
  "message",
  "modal",
  "notification",
  "pagination",
  "popover",
  "progress",
  "radio",
  "rate",
  "select",
  "skeleton",
  "slider",
  "space",
  "spin",
  "statistic",
  "steps",
  "switch",
  "table",
  "tabs",
  "tag",
  "time-picker",
  "timeline",
  "tooltip",
  "transfer",
  "tree",
  "tree-select",
  "upload",

  // 无样式
  // "config-provider",
  // "locale-provider",

  // 不会用到
  // "comment",

  // 很少用到
  // "form-model",
  // "page-header",
  // "result",
];

const OUTPUT_FOLDER = "less/antd";

/**
 * 生成 .less 文件
 * @param {string[]} fileNames 文件名
 * @param {string} outputFolder 输出目录
 */
function generateFiles(fileNames, outputFolder = ".") {
  const targetFolder = resolve(process.cwd(), outputFolder);
  const output = [];
  fileNames.forEach((fileName) => {
    const fileExtension = "less";
    const filePath = join(targetFolder, `${fileName}.${fileExtension}`);
    if (!existsSync(filePath)) {
      const content = `@import 'ant-design-vue/lib/${fileName}/style/index.less';\n`;
      writeFileSync(filePath, content);

      // 写入索引文件
      const indexFilePath = `${targetFolder}.${fileExtension}`;
      const relativeFilePath = `${basename(dirname(filePath))}/${fileName}`;
      appendFileSync(
        indexFilePath,
        `@import '${relativeFilePath}';\n`,
        "utf-8",
      );
    }
  });
  return output;
}

const run = () => {
  try {
    generateFiles(COMPONENTS, OUTPUT_FOLDER);
  } catch (error) {
    console.error(error);
  }
}

run();
