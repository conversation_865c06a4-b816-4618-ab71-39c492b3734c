@import 'ant-design-vue/lib/modal/style/index.less';

@modal-header-border-color-split: @qcc-color-gray-500;
@modal-footer-border-color-split: @qcc-color-gray-500;
@modal-body-padding: 15px;
@modal-base-gap: 15px;
@modal-body-gap-vertical: 15px;

@modal-body-padding: 12px 16px;
@modal-footer-padding-vertical: 12px;
@modal-footer-padding-horizontal: 16px;

// Drawer
@drawer-header-padding: 15px 20px;
@drawer-body-padding: 15px;

.ant-modal,
.ant-drawer {
  &-title {
    font-size: 15px;
    font-weight: @qcc-font-bold;
  }

  &-header {
    margin: 0 @modal-base-gap;
    padding: @modal-base-gap 0;
    border-bottom: 1px solid @modal-footer-border-color-split;

    &-inner {
      padding-right: 32px;
    }
  }

  &-header &-header-inner,
  &-footer &-footer-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-body {
    padding: @modal-body-gap-vertical @modal-base-gap;
  }

  &-close {
    width: 16px;
    height: 16px;
    padding: 0;
    // top: @modal-base-gap;
    // right: @modal-base-gap;
    top: 18px;
    right: 15px;
    color: @qcc-color-black-200;
    &:hover {
      color: @qcc-color-blue-500;
    }
    &-x {
      width: 16px;
      height: 16px;
      font-size: 16px;
      line-height: 1;
      display: block;
    }
  }

  &-footer {
    margin: 0 @modal-base-gap;
    padding: @modal-base-gap 0;
    border-top: 1px solid @modal-footer-border-color-split;

    button {
      min-width: 80px !important;
    }

    button + button {
      margin-bottom: 0;
      margin-left: 15px;
    }

    &-inner {
      height: 100%;
    }
  }
}

.ant-modal {
  top: 50px;

  &-content {
    border-radius: 4px;
  }
}

.ant-drawer {
  &-header {
    position: relative;
    border: none;

    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1px;
      background-color: #eee;
      content: '';
    }
  }

  &-body {
    padding: 0;
  }

  &-body-content {
    padding: 0 @drawer-body-padding;
  }

  &-title {
    font-weight: bold;
    line-height: 24px;
  }

  &-close {
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    color: #d6d6d6;
    font-size: 24px;

    &:hover {
      color: #b7b7b7;
    }
  }
}
