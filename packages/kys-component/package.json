{"name": "@kys/component", "description": "", "version": "0.0.1", "license": "MIT", "author": {"email": "<EMAIL>", "name": "Yandongxu"}, "homepage": "https://kys.qcc.com", "scripts": {"dev": "rollup -c --watch", "build": "rollup -c", "gulp": "gulp build", "build:types": "`tsc -p tsconfig.lib.json --outDir dist/types --composite false --declaration --emitDeclarationOnly`"}, "type": "module", "module": "dist/esm/index.js", "main": "dist/cjs/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "types": "./dist/types/index.d.ts"}}, "files": ["dist"], "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@kys/tsconfig": "workspace:*", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@rollup/plugin-url": "^8.0.2", "@types/node": "^22.0.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "ant-design-vue": "^1.7.8", "core-js": "^3.8.3", "glob": "^11.0.2", "gulp": "^5.0.1", "gulp-clean": "^0.4.0", "gulp-less": "^5.0.0", "postcss-url": "^10.1.3", "rollup": "^4.42.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "vue": "^2.7.16"}, "peerDependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "ant-design-vue": "^1.7.8", "less": "^4.2.2", "vue": "^2.7.16"}}