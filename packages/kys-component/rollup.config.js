import { fileURLToPath, URL } from 'url';
import { readFile } from 'node:fs/promises';
import alias from '@rollup/plugin-alias';
import { babel } from '@rollup/plugin-babel';
import commonjs from '@rollup/plugin-commonjs';
import nodeResolve from '@rollup/plugin-node-resolve';
import json from '@rollup/plugin-json';
import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import postcss from 'rollup-plugin-postcss';
import { defineConfig, rollup } from 'rollup';
import pkg from './package.json' with { type: 'json' };

const extensions = ['.js', '.jsx', '.ts', '.tsx'];
const styleExtensions = ['.less'];

const createInputConfig = (inputFile) => {
  const config = defineConfig({
    input: inputFile,
    jsx: 'preserve',
    plugins: [
      // 支持别名
      alias({
        entries: [
          {
            find: '@',
            replacement: fileURLToPath(new URL('src', import.meta.url)),
          },
        ],
      }),
      // 支持文件类型后缀
      nodeResolve({ extensions }),
      // 支持解析json文件
      json(),
      // 自动 external peerDependencies
      peerDepsExternal(),
      // 支持 CJS 格式的依赖
      commonjs({
        transformMixedEsModules: true,
        include: 'node_modules/**',
      }),
      // Babel 编译
      babel({
        extensions,
        babelrc: false,
        babelHelpers: 'bundled', // 'runtime' | 'bundled'
        presets: [
          [
            '@babel/preset-env',
            {
              // targets: 'defaults',
              modules: false,
            },
          ], // 保持 ES6 语法，不转换成 CommonJS
          ['@babel/preset-typescript'],
          ['@vue/babel-preset-jsx'], // Vue 2 JSX 支持
        ],
        exclude: 'node_modules/**',
      }),
      // Postcss: 仅支持 less 文件，不执行编译输出
      postcss({
        extensions: styleExtensions,
        inject: false,
        extract: false,
        use: {
          less: {
            javascriptEnabled: true,
          },
          sass: {},
          stylus: {},
        },
      }),
    ],
  });
  return config;
};

const createBanner = async () => {
  const banner = `/**
 * ${pkg.name}@${pkg.version} <${pkg?.homepage}>
 * Copyright (c) ${new Date().getFullYear()} ${pkg.author.name} <${pkg.author.email}>
 * Released under ${pkg.license} License
*/`;
  return banner;
};

/**
 * Rollup 打包
 */
export const createRollupBuild = async ({ format, input, outputDir }) => {
  const config = createInputConfig(input);
  const bundle = await rollup(config);
  const banner = await createBanner();

  await bundle.write({
    format,
    dir: outputDir,
    banner,
    // preserveModules: true,
    // preserveModulesRoot: 'src',
    // exports: 'named',
  });
};
