import { exec } from 'node:child_process';
import { parallel, series, src, dest } from 'gulp';
import less from 'gulp-less';
import clean from 'gulp-clean';
import { createRollupBuild } from './rollup.config.js';
import { globSync } from 'glob';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

/**
 * 复制less文件到编译目标目录
 */
const copyStyle = () => {
  return src('src/**/*.less').pipe(dest('dist/esm')).pipe(dest('dist/cjs'));
};

/**
 * 编译less文件
 */
const compileStyle = () => {
  return src('src/**/*.less')
    .pipe(less())
    .on('error', function (err) {
      console.error('Less 编译错误:', err.message);
      this.emit('end');
    })
    .pipe(dest('dist/esm'))
    .pipe(dest('dist/cjs'));
};

const inputFiles = Object.fromEntries(
  globSync('src/**/*.ts').map((file) => [
    path.relative('src', file.slice(0, file.length - path.extname(file).length)),
    fileURLToPath(new URL(file, import.meta.url)),
  ])
);

/**
 * 编译ESM格式
 */
const compileESM = async () => {
  await createRollupBuild({
    input: inputFiles,
    format: 'esm',
    outputDir: 'dist/esm',
  });
};

/**
 * 编译CJS格式
*/
const compileCJS = async () => {
  await createRollupBuild({
    input: inputFiles,
    format: 'cjs',
    outputDir: 'dist/cjs',
  });
};

/**
 * 编译ts类型文件
 */
const compileTypes = (cb) => {
  const outDir = 'dist/types';
  exec(`npx tsc -p tsconfig.lib.json --outDir ${outDir} --allowImportingTsExtensions false --noEmit false --declaration --emitDeclarationOnly`, (error, stdout, stderr) => {
    if (error) {
      console.error('TypeScript 编译错误:', error);
      console.error('stderr:', stderr);
      cb(error);
      return;
    }
    if (stderr) {
      console.warn('TypeScript 编译警告:', stderr);
    }
    if (stdout) {
      console.log('TypeScript 编译输出:', stdout);
    }
    cb();
  });
};

/**
 * 清理目标文件夹
 */
export const clearFolder = () => {
  return src(['dist/esm', 'dist/cjs', 'dist/types'], { read: false, allowEmpty: true }).pipe(clean());
};
export { compileTypes };
export const processStyle = parallel(copyStyle, compileStyle);
export const processScript = parallel(compileESM, compileCJS, compileTypes);
export const build = series(clearFolder, processStyle, processScript);
