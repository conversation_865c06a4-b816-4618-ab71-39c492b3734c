// 组件命名空间
@prefix: q;

// 颜色系统
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 间距系统
@spacing-unit: 8px;
@spacing-small: @spacing-unit;
@spacing-medium: @spacing-unit * 2;
@spacing-large: @spacing-unit * 3;

// 字体系统
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;

:root {
  // 颜色系统
  --primary-color: @primary-color;
  --success-color: @success-color;
  --warning-color: @warning-color;
  --error-color: @error-color;

  // 间距系统
  --spacing-unit: @spacing-unit;
  --spacing-small: @spacing-small;
  --spacing-medium: @spacing-medium;
  --spacing-large: @spacing-large;

  // 字体系统
  --font-size-base: @font-size-base;
  --font-size-lg: @font-size-lg;
  --font-size-sm: @font-size-sm;
}