@import (reference) "../../style/token.less";
@import "../../style/mixin.less";

@button-class: ~'@{prefix}-button';

// 按钮圆角
@button-radius: @spacing-small;
@button-size: 100px;

:root {
  --button-size: @button-size;
  --button-radius: @button-radius;
}

.@{button-class} {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  width: var(--button-size);

  &:hover {
    background-color: red;
  }
}