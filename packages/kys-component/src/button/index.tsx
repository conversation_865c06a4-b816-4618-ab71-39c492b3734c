import { defineComponent } from 'vue';

const Button = defineComponent({
  name: 'Q<PERSON>utt<PERSON>',
  emits: ['click'],
  props: {
    type: {
      type: String,
      default: 'primary',
    },
  },
  setup(props, { emit }) {
    const onClick = () => {
      emit('click');
    };
    return {
      onClick,
    };
  },
  render() {
    return (
      <div class="q-button" onClick={this.onClick}>
        {this.$slots.default} ({this.type})
      </div>
    );
  },
});

export default Button;
